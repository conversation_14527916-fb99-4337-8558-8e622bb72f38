/* 全局基础样式重置，去除默认内外边距 */
html,
body,
aside,
div,
p,
nav,
a,
i,
ul,
li,
section,
img,
footer {
  padding: 0;
  margin: 0;
}

/* 去除列表前的默认小圆点 */
li {
  list-style: none;
}

/* 去除链接默认下划线 */
a {
  text-decoration: none;
}

/* 设置基础字体、颜色、字体族 */
body {
  font-size: 12px;
  color: #4d4d4d;
  font-family: Helvetica Neue, Microsoft Yahei;
}

/* 设置背景颜色 */
body,
.main,
.container {
  background: #f3f6f8;
}

/* 图标字体统一样式 */
.iconfont {
  color: #6b7386;
  padding-right: 4px;
}

/* 左侧边栏容器样式 */
.container .left-bar {
  position: fixed;
  background: #30333c;
  color: #6b7386;
  box-sizing: border-box;
  flex-direction: column;
  height: 100vh;
  display: flex;
  width: 248px;
  transition: all .5s;
}

/* 主体部分布局设置，设置 margin-left 给出空间给左侧边栏 */
.container .main {
  position: relative;
  display: flex;
  flex-direction: column;
  margin-left: 248px;
}

/* 主体内图标大小及颜色调整 */
.container .main .iconfont {
  font-size: 18px;
  color: #000000;
}

/* 自定义滚动条样式 */
.set-scroll {
  background-color: #494f5e;
  border-radius: 0;
  width: 4px;
}

/* logo 容器大小设置 */
.left-bar .big-logo {
  width: 240px;
  height: 230px;
  overflow: hidden; /* 隐藏超出部分 */
}

/* logo 图片样式设置（含滤镜和边框） */
.left-bar .big-logo img {
  filter: drop-shadow(3px 0 0 #fff);
  transform: translateX(-20px);
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
}

/* 文字描述（副标题）样式 */
.left-bar .ti-sec {
  text-align: center;
  font-size: 12px;
  line-height: 12px;
  color: #fff;
}

/* 左侧标题样式 */
.left-bar .title {
  color: white;
  font-size: 18px;
  height: 50px;
  line-height: 50px;
}
.left-bar .title p {
  text-align: center;
}

/* 左侧导航栏容器样式 */
.left-bar .nav {
  flex: 1;
  overflow-y: auto;
}

/* 每个导航项样式 */
.left-bar .nav .item {
  height: 48px;
  line-height: 46px;
  border-top: 2px solid rgba(255, 255, 255, 0.04);
  border-bottom: 2px solid rgba(255, 255, 255, 0.04);
  text-align: center;
  width: 248px;
}

/* 导航图标左内边距 */
.left-bar .nav .item .icp {
  padding-left: 20px;
}

/* 激活项右侧高亮标识条 */
.left-bar .nav > .active .line {
  position: absolute;
  right: 0px;
  height: 30px;
  width: 3px;
  background: #f3f6f8;
  z-index: 100000;
  margin-top: 10px;
}

/* 导航项容器高度设置 */
.left-bar .nav-item {
  min-height: 100vh;
}

/* 图标与文字之间间距 */
.left-bar .nav-item span {
  margin-right: 8px;
}

/* 自定义滚动条轨道样式 */
.left-bar .nav-item::-webkit-scrollbar,
.left-bar .nav-item::-webkit-scrollbar-track,
.left-bar .nav-item::-webkit-scrollbar-thumb {
  background-color: #494f5e;
  border-radius: 0;
  width: 4px;
}

/* 导航项内部列表样式 */
.left-bar .nav-item li {
  padding-left: 12px;
  height: 50px;
  line-height: 50px;
}

/* 激活状态或 hover 时的样式 */
.left-bar .nav-item li > .active,
.left-bar .nav-item li > .active i,
.left-bar .nav-item li:hover a,
.left-bar .nav-item li:hover i {
  color: #30333c;
  background: #F3F6F8;
  padding-left: 12px;
}

/* 链接样式 */
.left-bar .nav-item li a {
  font-size: 14px;
  display: inline-block;
  width: 224px;
  color: #6b7386;
  white-space: nowrap;
  padding-left: 20px;
}

/* 导航主链接颜色 */
.nav .item a {
  color: white;
}

/* 固定底部评论区 */
.nav .comment {
  position: fixed;
  z-index: 100;
  bottom: 1px;
  width: 200px;
  background: #30333c;
}

/* 主体内容最大宽度限制 */
.main #mainContent {
  max-width: 1920px;
}

/* “关于”区域样式 */
.main .about {
  padding: 20px;
  font-size: 14px;
  line-height: 28px;
}
.main .about i {
  margin-right: 5px;
}

/* 通用内容卡片容器样式 */
.main .box {
  overflow: hidden;
  margin: 20px 30px;
  background: #fff;
  padding-bottom: 20px;
}

/* 用户盒子额外样式 */
.main .box_user {
  background: #f2f2f2;
  border: 1px solid #fff;
}

/* 子分类标题区域样式 */
.main .box .sub-category > div {
  padding: 10px 0 10px 2.1%;
  font-size: 16px;
  border-bottom: 2px solid #F3F6F8;
}
.main .box .sub-category div i,
.main .box .sub-category div span {
  padding-right: 6px;
}

/* 每个展示项目的盒子样式 */
.main .box .item {
  width: 20%;
  border: 1px solid #e4ecf3;
  box-shadow: 1px 2px 3px #f2f6f8;
  border-radius: 6px;
  background: #fff;
  padding: 10px;
  min-width: 200px;
  float: left;
  overflow: hidden;
  transition: all .3s;
}

/* 用户盒子阴影样式 */
.main .box .item_user {
  box-shadow: 1px 2px 3px rgba(229,229,229,.5);
}

/* hover 时向上浮动效果 */
.main .box .item:hover {
  transform: translateY(-5px);
}

/* 无 logo 情况下的文字显示 */
.main .box .item .no-logo {
  color: #3273dc;
  font-weight: bold;
  font-size: 14px;
}

/* logo 图片样式 */
.main .box .item .logo {
  height: 40px;
  position: absolute;
  font-size: 14px;
  color: #3273dc;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 .1rem;
}
.main .box .item .logo img {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  vertical-align: middle;
  margin-right: 5px;
  border: 1px solid #fff;
}

/* 内容文字区域样式 */
.main .box .item .content {
  color: gray;
  font-size: 13px;
  padding-left: 43px;
  height: 60px;
  overflow: hidden;
  text-overflow: clip;
}

/* 额外描述文字样式 */
.main .box .item .content_d {
  color: blue;
  font-size: 12px;
  padding-left: 44px;
  height: 32px;
  line-height: 32px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 底部额外描述区域样式 */
.main .box .item .desc {
  font-size: 13px;
  padding-top: 5px;
}

/* 页脚样式 */
.footer {
  width: 100%;
  bottom: 0;
  line-height: 45px;
  background: #fff;
  box-shadow: 0 0 1rem rgba(0, 0, 0, 0.2);
}
.footer .copyright {
  margin-left: 30px;
  color: #949494;
  font-size: 12px;
}
.footer .copyright a {
  text-decoration: none;
  color: #3a85ff;
}

/* 移动端菜单样式 */
#menu {
  display: none;
  width: 100%;
  background: #fff;
  padding: 10px;
  text-align: left;
  border-bottom: 3px solid #F3F6F8;
}
#menu a {
  color: #3a85ff;
  font-size: 18px;
}

/* 下载相关颜色高亮 */
.download a,
.download i {
  color: #3668bd !important;
}

/* 右下角固定按钮 */
#fixedBar {
  padding: 10px;
  background: #fff;
  width: 25px;
  height: 25px;
  position: fixed;
  right: 10px;
  bottom: 30px;
  z-index: 9999;
  box-shadow: 0 0 20px #ccc;
  cursor: pointer;
  display: none;
}
#fixedBar svg {
  color: #9e9e9e;
}

/* 响应式布局：小于 599px 的设备样式 */
@media screen and (max-width: 599px) {
  .container .box .item {
    width: 39%;
    min-width: 100px;
    margin: 8px 0 0 2.1%;
  }
  .container .main {
    margin-left: 0;
  }
  .main .box .item .desc {
    height: 18px;
  }
  .container .left-bar {
    z-index: 999;
    left: -249px;
  }
  #menu {
    display: block;
    position: fixed;
    z-index: 200;
  }
  .left-bar .nav-item li {
    height: 40px;
    line-height: 40px;
  }
}

/* 响应式布局：600px 及以上 */
@media screen and (min-width: 600px) {
  .container .box .item {
    width: 90%;
    margin: 8px 0 0 2.1%;
  }
  .container .left-bar {
    left: 0px;
  }
}

/* 响应式布局：790px 及以上 */
@media screen and (min-width: 790px) {
  .container .box .item {
    width: 43%;
    margin: 18px 0 0 2.1%;
  }
}

/* 响应式布局：1040px 及以上 */
@media screen and (min-width: 1040px) {
  .container .box .item {
    width: 28%;
    margin: 22px 0 0 2.1%;
  }
  .container .left-bar {
    left: 0px;
  }
}

/* 响应式布局：1200px 及以上 */
@media screen and (min-width: 1200px) {
  .container .box .item {
    width: 20%;
    margin: 22px 0 0 2.1%;
  }
}
