<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="description" content="𝓌𝑜𝒷网址导航" />
    <link rel="shortcut icon" href="static/logo.svg" type="image/svg+xml">
    <meta name="keywords" content="网址导航" />
    <title>𝓌𝑜𝒷网址导航</title>
    <!-- 引入外部样式表，用于基本布局和通用样式 -->
    <link rel="stylesheet" href="static/style.css">
    <!-- 引入 Themify Icons 字体图标库 -->
    <link rel="stylesheet" href="static/themify-icons.css">

    <!-- 内联样式表，用于覆盖或添加特定样式，特别是响应式和布局调整 -->
    <style>
        /* 针对大Logo图片和其容器的样式调整 */
        .big-logo {
            /* 保持之前修改，使其填充满容器且不留边距 */
            /* text-align: center; */ /* 移除，因为图片已设width:100%且是block，不需要text-align居中 */
            width: 100%; /* 宽度占满父容器，使其图片撑满250px的left-bar */
            /* max-width: 200px; */ /* 已移除，确保图片可以填充满left-bar的全部宽度 */
            margin: 0 0 5px 0; /* 移除水平自动外边距，使内容紧贴左右边缘。仅保留底部外边距5px。 */
            aspect-ratio: 302 / 306; /* 保持图片的宽高比 */
            overflow: hidden; /* 防止内容溢出 */
        }

        .big-logo img {
            width: 100%; /* 图片宽度填充父容器 */
            height: 100%; /* 图片高度填充父容器 */
            object-fit: cover; /* 裁剪图片以填充容器，保持宽高比 */
            display: block; /* 移除图片底部默认的空白间隙 */
        }

        /* ------------------------------------------- */
        /* 基础布局和主题颜色样式 */
        body {
            margin: 0; /* 移除body默认外边距，确保内容从屏幕边缘开始 */
            background-color: #f0f2f5; /* 页面背景色 */
            color: #333333; /* 默认文字颜色 */
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; /* 字体栈 */
            transition: padding-left 0.3s ease-in-out; /* 为菜单打开时的padding-left变化添加过渡效果（虽然这里没有实际用到padding-left） */
        }

        body.menu-open {
            overflow: hidden; /* 菜单打开时，禁止body滚动，防止背景内容滚动 */
        }

        .container {
            display: flex; /* 使用 Flexbox 布局，使左侧栏和主内容区并排显示 */
            min-height: 100vh; /* 容器最小高度为视口高度，确保页面即使内容较少也能撑满屏幕 */
        }

        /* 左侧导航栏样式 */
        .left-bar {
            width: 250px; /* 固定左侧栏宽度 */
            background-color: #fff; /* 背景色为白色 */
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1); /* 右侧阴影，增加立体感 */
            /* MODIFIED: PC端样式 - 移除左侧栏的外部填充，使其内部内容直接贴边 */
            padding: 0; 
            box-sizing: border-box; /* 边框盒模型，padding和border不增加元素总尺寸 */
            display: flex; /* 内部使用 Flexbox 布局 */
            flex-direction: column; /* 垂直排列子元素 */
            transition: transform 0.3s ease-in-out, left 0.3s ease-in-out; /* 菜单滑入滑出的过渡效果 */
            position: relative; /* 相对定位，为子元素（如关闭按钮）的绝对定位提供参考 */
            z-index: 1000; /* 层级高于主内容区 */
        }
        
        /* 移动端菜单关闭按钮样式 */
        .close-menu-btn {
            display: none; /* 默认不显示，只在移动端显示 */
            position: absolute; /* 绝对定位 */
            top: 10px; /* 距离顶部10px */
            right: 15px; /* 距离右侧15px */
            font-size: 2em; /* 字体大小 */
            color: #333; /* 颜色 */
            text-decoration: none; /* 移除下划线 */
            line-height: 1; /* 行高 */
            z-index: 1101; /* 层级高于左侧栏内容 */
        }

        /* 网站标题样式 */
        .title {
            text-align: center; /* 文本居中 */
            /* MODIFIED: PC端样式 - 移除底部外边距，让标题更紧贴容器顶部或Logo */
            margin: 0; /* 从 margin-bottom: 20px; 改变 */
            font-size: 1.4em; /* 字体大小 */
            color: #333333; /* 文本颜色 */
            padding: 0; /* 默认内边距为0 */
            box-sizing: border-box;
        }

        /* 导航列表样式 */
        .nav-item {
            list-style: none; /* 移除列表项前面的点 */
            padding: 0; /* 移除列表默认内边距 */
            margin: 0; /* 移除列表默认外边距 */
            flex-grow: 1; /* 占据可用空间，使底部评论区固定在底部 */
            overflow-y: auto; /* 当内容溢出时，允许垂直滚动 */
        }

        /* 导航链接样式 */
        .nav-item li a {
            display: block; /* 使链接填充整个列表项区域，便于点击 */
            padding: 12px 15px; /* 默认内边距，确保导航文字可读性，这属于内容内部间距 */
            text-decoration: none; /* 移除下划线 */
            color: #333333; /* 默认文字颜色 */
            border-radius: 0; /* 移除圆角，避免视觉上的“缝隙” */
            transition: background-color 0.3s ease; /* 背景色变化的过渡效果 */
        }

        /* 导航链接悬停和激活时的样式 */
        .nav-item li a:hover,
        .nav-item li a.active-link {
            background-color: #e9e9e9; /* 悬停/激活时的背景色 */
            color: #007bff; /* 悬停/激活时的文字颜色 */
        }

        /* 底部评论区/ICP信息样式 */
        .item.comment {
            /* MODIFIED: PC端样式 - 移除顶部外边距，使其紧贴上方导航区 */
            margin: 0; /* 从 margin-top: 20px; 改变 */
            text-align: center; /* 文本居中 */
            flex-shrink: 0; /* 不会被压缩 */
            padding: 0; /* 默认内边距为0 */
            box-sizing: border-box;
        }

        .icp a {
            color: #999999; /* 链接颜色 */
            text-decoration: none; /* 移除下划线 */
            font-size: 0.9em; /* 字体大小 */
        }

        /* 主内容区样式 */
        .main {
            flex-grow: 1; /* 占据剩余所有可用空间 */
            /* MODIFIED: PC端样式 - 移除主内容区的外部填充，使其内容充满可用空间 */
            padding: 0; /* 从 20px 改变 */
            position: relative; /* 相对定位，为内部元素（如菜单切换按钮）的绝对定位提供参考 */
            transition: filter 0.3s ease-in-out; /* 滤镜效果的过渡（如果使用了） */
        }
        body.menu-open .main {
            /* filter: blur(2px) brightness(0.7); */
        }

        /* 移动端菜单切换按钮样式 */
        #menu-toggle {
            display: none; /* 默认不显示，只在移动端显示 */
            font-size: 1.8em; /* 字体大小 */
            padding: 10px; /* 内边距 */
            position: fixed; /* 固定定位，不随滚动条滚动 */
            top: 10px; /* 距离顶部10px */
            left: 10px; /* 距离左侧10px */
            z-index: 1001; /* 层级高于普通内容 */
            background-color: rgba(255,255,255,0.8); /* 半透明背景 */
            border-radius: 5px; /* 圆角 */
            box-shadow: 0 2px 5px rgba(0,0,0,0.1); /* 阴影 */
            color: #333; /* 颜色 */
            cursor: pointer; /* 鼠标悬停时显示手型指针 */
        }
        #menu-toggle i {
            display: block; /* 使图标块级显示 */
        }

        /* "关于"区域盒子样式 */
        .about.box {
            background-color: #fff; /* 背景色 */
            padding: 20px; /* 内边距，保持内容块内部填充，有助于可读性 */
            border-radius: 8px; /* 圆角 */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* 阴影 */
            margin-bottom: 20px; /* 底部外边距，与下方内容块隔开 */
        }

        .about.box p {
            color: #333333; /* 文字颜色 */
            margin-top: 0; /* 移除顶部外边距 */
            margin-bottom: 15px; /* 底部外边距 */
            font-size: 1.1em; /* 字体大小 */
            line-height: 1.6; /* 行高 */
        }
        .about.box .center-image-container {
            /* 保持text-align: center; 以确保对inline/inline-block子元素居中，
               辅助block元素图片的 margin: auto; */
            text-align: center; 
        }
        .about.box .center-image-container img {
            max-width: 100%; /* 图片最大宽度不超过父容器 */
            width: 100%; /* 图片宽度填充父容器的100% */
            /* MODIFIED: 调整图片显示大小 - 减小 max-width 值为 300px，使其显示小一点 */
            max-width: 300px; /* 原为 400px */
            height: auto; /* 高度自动调整，保持图片原始比例 */
            object-fit: cover; /* 裁剪图片以填充容器，保持宽高比。如果容器宽高比与图片不同，图片会被裁剪。 */
            display: block; /* 块级显示，以便应用 margin: auto 进行水平居中 */
            margin: 0 auto; /* 水平居中图片，上下外边距为0 */
            pointer-events: none; /* 禁止鼠标事件，如点击拖拽 */
            user-select: none; /* 禁止用户选择文本或图片 */
        }

        /* 页面底部页脚样式 */
        .footer {
            text-align: center; /* 文本居中 */
            padding: 20px 0; /* 上下内边距20px，左右0，保持内容块内部填充 */
            color: #777777; /* 文字颜色 */
            font-size: 0.9em; /* 字体大小 */
        }

        .footer a {
            color: #999999; /* 链接颜色 */
            text-decoration: none; /* 移除下划线 */
        }
         .footer a:hover {
            text-decoration: underline; /* 鼠标悬停时显示下划线 */
        }

        /* 回到顶部按钮样式 */
        #fixedBar {
            position: fixed; /* 固定定位，不随滚动条滚动 */
            bottom: 20px; /* 距离底部20px */
            right: 20px; /* 距离右侧20px */
            cursor: pointer; /* 鼠标悬停时显示手型指针 */
            color: #555; /* 图标颜色 */
            background-color: rgba(255,255,255,0.8); /* 半透明背景 */
            border-radius: 50%; /* 圆形 */
            padding: 8px; /* 内边距 */
            box-shadow: 0 2px 5px rgba(0,0,0,0.15); /* 阴影 */
            transition: background-color 0.3s, color 0.3s; /* 背景色和颜色变化的过渡效果 */
        }
        #fixedBar:hover {
            background-color: #007bff; /* 悬停时背景色 */
            color: #fff; /* 悬停时图标颜色 */
        }

        #fixedBar svg {
            width: 24px; /* SVG图标宽度 */
            height: 24px; /* SVG图标高度 */
            display: block; /* 块级显示 */
        }

        /* 主题切换按钮样式 */
        #themeToggle {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ffd700, #ff6b35);
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            z-index: 1002;
        }

        #themeToggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        /* 暗色主题样式 */
        body.dark-theme {
            background-color: #1a1a1a;
            color: #e0e0e0;
        }

        body.dark-theme .left-bar {
            background-color: #2d2d2d;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.3);
        }

        body.dark-theme .about.box,
        body.dark-theme .card-item {
            background-color: #2d2d2d;
            color: #e0e0e0;
        }

        body.dark-theme .nav-item li a {
            color: #e0e0e0;
        }

        body.dark-theme .nav-item li a:hover,
        body.dark-theme .nav-item li a.active-link {
            background-color: #404040;
            color: #4fc3f7;
        }

        body.dark-theme #themeToggle {
            background: linear-gradient(45deg, #4a90e2, #7b68ee);
        }

        /* 状态栏样式 */
        .status-bar {
            position: fixed;
            top: 80px;
            right: 20px;
            background: rgba(255,255,255,0.95);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            min-width: 200px;
            z-index: 1001;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        body.dark-theme .status-bar {
            background: rgba(45,45,45,0.95);
            color: #e0e0e0;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }

        body.dark-theme .status-item {
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .status-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .status-label {
            font-weight: 500;
        }

        .status-value {
            color: #007bff;
            font-weight: 600;
        }

        body.dark-theme .status-value {
            color: #4fc3f7;
        }

        /* 实时时钟样式 */
        .clock-widget {
            position: fixed;
            bottom: 80px;
            right: 20px;
            background: rgba(255,255,255,0.95);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            z-index: 1001;
            min-width: 150px;
        }

        body.dark-theme .clock-widget {
            background: rgba(45,45,45,0.95);
            color: #e0e0e0;
        }

        .clock-time {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        body.dark-theme .clock-time {
            color: #4fc3f7;
        }

        .clock-date {
            font-size: 12px;
            color: #666;
        }

        body.dark-theme .clock-date {
            color: #ccc;
        }

        /* 快捷搜索框样式 */
        .search-widget {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            z-index: 2000;
            display: none;
            min-width: 400px;
        }

        body.dark-theme .search-widget {
            background: rgba(45,45,45,0.95);
            color: #e0e0e0;
        }

        .search-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            border-color: #007bff;
        }

        body.dark-theme .search-input {
            background: #404040;
            border-color: #555;
            color: #e0e0e0;
        }

        body.dark-theme .search-input:focus {
            border-color: #4fc3f7;
        }

        .search-engines {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .search-engine {
            padding: 8px 15px;
            background: #f0f0f0;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .search-engine:hover {
            background: #007bff;
            color: white;
        }

        body.dark-theme .search-engine {
            background: #555;
            color: #e0e0e0;
        }

        body.dark-theme .search-engine:hover {
            background: #4fc3f7;
        }

        /* 天气信息样式 */
        .weather-widget {
            position: fixed;
            top: 140px;
            right: 20px;
            background: rgba(255,255,255,0.95);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            z-index: 1001;
            min-width: 180px;
            text-align: center;
        }

        body.dark-theme .weather-widget {
            background: rgba(45,45,45,0.95);
            color: #e0e0e0;
        }

        .weather-icon {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .weather-temp {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }

        body.dark-theme .weather-temp {
            color: #4fc3f7;
        }

        .weather-desc {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        body.dark-theme .weather-desc {
            color: #ccc;
        }

        /* 便签功能样式 */
        .notes-widget {
            position: fixed;
            bottom: 140px;
            right: 20px;
            background: rgba(255,255,255,0.95);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            z-index: 1001;
            width: 200px;
            max-height: 300px;
            overflow-y: auto;
        }

        body.dark-theme .notes-widget {
            background: rgba(45,45,45,0.95);
            color: #e0e0e0;
        }

        .notes-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 14px;
        }

        .notes-input {
            width: 100%;
            min-height: 60px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 8px;
            font-size: 12px;
            resize: vertical;
            outline: none;
        }

        body.dark-theme .notes-input {
            background: #404040;
            border-color: #555;
            color: #e0e0e0;
        }

        .notes-list {
            margin-top: 10px;
        }

        .note-item {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 8px;
            margin-bottom: 5px;
            font-size: 12px;
            position: relative;
            cursor: pointer;
        }

        body.dark-theme .note-item {
            background: #555;
        }

        .note-delete {
            position: absolute;
            top: 2px;
            right: 5px;
            color: #999;
            cursor: pointer;
            font-size: 14px;
        }

        .note-delete:hover {
            color: #ff4757;
        }

        /* 音乐播放器样式 */
        .music-widget {
            position: fixed;
            bottom: 200px;
            right: 20px;
            background: rgba(255,255,255,0.95);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            z-index: 1001;
            width: 200px;
            text-align: center;
        }

        body.dark-theme .music-widget {
            background: rgba(45,45,45,0.95);
            color: #e0e0e0;
        }

        .music-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 10px;
        }

        .music-btn {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .music-btn:hover {
            background: #0056b3;
            transform: scale(1.1);
        }

        body.dark-theme .music-btn {
            background: #4fc3f7;
        }

        body.dark-theme .music-btn:hover {
            background: #29b6f6;
        }

        /* 帮助面板样式 */
        .help-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255,255,255,0.98);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            z-index: 3000;
            display: none;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        body.dark-theme .help-panel {
            background: rgba(45,45,45,0.98);
            color: #e0e0e0;
        }

        .help-header {
            text-align: center;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }

        body.dark-theme .help-header {
            color: #4fc3f7;
        }

        .help-section {
            margin-bottom: 15px;
        }

        .help-section h4 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 14px;
        }

        body.dark-theme .help-section h4 {
            color: #e0e0e0;
        }

        .help-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            font-size: 13px;
            border-bottom: 1px solid #eee;
        }

        body.dark-theme .help-item {
            border-bottom: 1px solid #555;
        }

        .help-key {
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }

        body.dark-theme .help-key {
            background: #555;
            color: #e0e0e0;
        }

        .help-close {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }

        .help-close:hover {
            color: #333;
        }

        body.dark-theme .help-close:hover {
            color: #e0e0e0;
        }

        /* 新增：动态加载内容区域的样式，特别是卡片布局 */
        #dynamicPageContent {
            display: grid; /* 使用 CSS Grid 布局 */
            /* 自动填充列，每列最小250px，最大1fr (占据剩余空间) */
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); 
            gap: 20px; /* 卡片之间的间距 */
            /* 因为 .main 的 padding 移除了，这里给卡片区域加上内边距，确保内容不贴边 */
            padding: 20px; 
        }

        /* 单个卡片项的样式 */
        .card-item {
            background-color: #fff; /* 背景色 */
            border-radius: 8px; /* 圆角 */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* 阴影 */
            padding: 15px; /* 内边距 */
            display: flex; /* 使用 flexbox 布局卡片内部元素 */
            flex-direction: column; /* 垂直排列图标和文本 */
            align-items: flex-start; /* 内容左对齐 */
        }

        /* 卡片图标容器样式 */
        .card-item .card-icon {
            width: 50px; /* 图标容器的宽度 */
            /* MODIFIED for t2.jpeg: 拉长图标容器的高度，使其能容纳被拉伸的图片 */
            height: 70px; /* 增加高度以允许图片“拉长”或为文本提供更多空间 */
            margin-bottom: 10px; /* 图标和标题之间的间距 */
            display: flex; /* 使用 flexbox 居中图标 */
            justify-content: center; /* 水平居中 */
            align-items: center; /* 垂直居中 */
        }

        /* 卡片图标图片样式 */
        .card-item .card-icon img {
            max-width: 100%; /* 图片最大宽度不超过容器 */
            max-height: 100%; /* 图片最大高度不超过容器 */
            /* MODIFIED for t2.jpeg: 图片拉长，填充满其容器 */
            object-fit: fill; /* 这将拉伸图片以填充其容器的尺寸，可能会导致图片变形。 */
            /* 如果您希望图片保持比例而不变形，但仍然占据容器高度，请使用 object-fit: contain; */
            /* object-fit: contain; */ 
        }

        /* 卡片标题样式 */
        .card-item h3 {
            font-size: 1.1em; /* 字体大小 */
            margin-top: 0; /* 移除顶部外边距 */
            margin-bottom: 5px; /* 底部外边距 */
            color: #333; /* 颜色 */
        }

        /* 卡片描述文字样式 */
        .card-item p.card-description {
            font-size: 0.9em; /* 字体大小 */
            color: #666; /* 颜色 */
            line-height: 1.4; /* 行高，确保文字行高足够，防止文字堆叠 */
            margin: 0; /* 移除默认外边距 */
            /* 确保文字不会被裁剪，并正常显示多行 */
            white-space: normal; /* 允许文本自动换行 */
            overflow: visible; /* 阻止内容被隐藏 */
            text-overflow: clip; /* 文本溢出时，不显示省略号（剪裁） */
            /* 如果您需要限制描述为2行并显示省略号，可以取消以下注释： */
            /* display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden; */
        }


        /* 媒体查询，用于响应式设计 (移动端样式) */
        @media (max-width: 768px) {
            /* 移动端左侧栏样式 */
            .left-bar {
                position: fixed; /* 固定定位，覆盖整个屏幕 */
                top: 0; /* 贴紧顶部 */
                left: -100%; /* 默认隐藏在屏幕左侧之外 */
                width: 100%; /* 宽度占满屏幕 */
                height: 100vh; /* 高度占满视口 */
                z-index: 1100; /* 高层级，确保覆盖内容 */
                box-shadow: 2px 0 10px rgba(0,0,0,0.2); /* 阴影 */
                /* 移动端左侧栏的填充保持不变，因为它需要为关闭按钮保留顶部空间，以及其他内部元素的填充 */
                padding: 50px 0 0 0; /* 顶部内边距50px，左右下为0 */
                margin: 0; /* 移除外边距 */
                box-sizing: border-box;
            }

            .left-bar.active {
                left: 0; /* 当菜单激活时，滑入屏幕 */
            }
            
            /* 移动端菜单关闭按钮显示 */
            .close-menu-btn {
                display: block; /* 在移动端显示关闭按钮 */
                right: 10px; /* 距离右侧10px */
            }

            /* 移动端菜单切换按钮显示 */
            #menu-toggle {
                display: block; /* 在移动端显示菜单切换按钮 */
            }
            
            /* MODIFIED: 移除移动端左侧栏内部所有内容的填充/外边距，实现内容真正贴边 */
            .big-logo {
                max-width: 100%; /* 在移动端 Logo 宽度填充100% */
                margin: 0; /* 无外边距 */
                padding: 0; /* 无内边距 */
                box-sizing: border-box;
            }
             .title {
                font-size: 1.2em; /* 移动端标题字体略小 */
                margin: 0; /* 无外边距 */
                padding: 0; /* 无内边距 */
            }
            .nav-item {
                padding: 0; /* 无内边距 */
                margin: 0; /* 无外边距 */
            }
            .nav-item li a {
                padding: 0; /* 移动端导航链接无内边距，文字会贴着边缘 */
                border-radius: 0; /* 确保无圆角，避免视觉缝隙 */
            }
            .item.comment {
                margin: 0; /* 无外边距 */
                padding: 0; /* 无内边距 */
            }
            .item.comment .icp {
                margin: 0; /* 无外边距 */
                padding: 0; /* 无内边距 */
            }
            .item.comment .icp a {
                padding: 0; /* 无内边距 */
            }
            /* 移动端卡片区域的内边距，相比PC端略小，以适应小屏幕 */
            #dynamicPageContent {
                padding: 10px;
            }

            /* 移动端主题切换按钮调整 */
            #themeToggle {
                top: 15px;
                right: 15px;
                width: 45px;
                height: 45px;
                font-size: 20px;
            }

            /* 移动端状态栏调整 */
            .status-bar {
                top: 70px;
                right: 15px;
                padding: 10px;
                min-width: 160px;
                font-size: 12px;
            }

            /* 移动端时钟组件调整 */
            .clock-widget {
                bottom: 70px;
                right: 15px;
                padding: 10px;
                min-width: 120px;
            }

            .clock-time {
                font-size: 16px;
            }

            .clock-date {
                font-size: 11px;
            }

            /* 移动端搜索框调整 */
            .search-widget {
                min-width: 300px;
                padding: 15px;
            }

            /* 移动端天气组件调整 */
            .weather-widget {
                top: 120px;
                right: 15px;
                padding: 10px;
                min-width: 140px;
            }

            .weather-icon {
                font-size: 20px;
            }

            .weather-temp {
                font-size: 16px;
            }

            /* 移动端便签组件调整 */
            .notes-widget {
                bottom: 120px;
                right: 15px;
                width: 160px;
                padding: 10px;
            }

            /* 移动端音乐播放器调整 */
            .music-widget {
                bottom: 160px;
                right: 15px;
                width: 160px;
                padding: 10px;
            }

            .music-btn {
                width: 30px;
                height: 30px;
            }
        }
    </style>
</head>

<body>
    <!-- 主容器，包含左侧导航栏和主内容区 -->
    <div class="container" id="container">
        <!-- 左侧导航栏 -->
        <aside class="left-bar" id="leftBar">
            <!-- 移动端关闭菜单按钮 -->
            <a href="javascript:void(0);" class="close-menu-btn" id="closeMenuBtn">×</a>
            <!-- 网站大Logo区域 -->
            <div class="big-logo">
                <video autoplay loop muted playsinline
                       ondragstart="return false;"
                       oncontextmenu="return false;"
                       style="width: 100%; height: 100%; object-fit: cover; display: block;">
                    <source src="static/favtion.mp4" type="video/mp4">
                    <!-- 如果浏览器不支持视频，显示备用图片 -->
                    <img src="https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/image/GIF/35465.gif"
                         ondragstart="return false;"
                         onmousedown="return false;"
                         alt="WOB Logo"
                         style="width: 100%; height: 100%; object-fit: cover; display: block;">
                </video>
            </div>
            <!-- 网站标题 -->
            <div class="title" style="font-family: 'STKaiti', '华文楷体', serif;">
                <p>𝓌𝑜𝒷 网址导航</p>
            </div>
            <!-- 导航菜单 -->
            <nav class="nav">
                <ul class="nav-item" id="navItem">
                    <!-- 导航项将由 JS 动态填充 -->
                </ul>
            </nav>
            <!-- 底部评论/ICP信息 -->
            <div class="item comment">
                <p class="icp"><a href="https://wob-nit.pages.dev/">𝓌𝑜𝒷导航网站</a></p>
            </div>
        </aside>

        <!-- 主内容区域 -->
        <section class="main" id="mainSection">
            <!-- 移动端菜单切换按钮 -->
            <div id="menu-toggle">
                <i class="ti-menu-alt"></i>
            </div>

            <!-- 主题切换按钮 -->
            <button id="themeToggle" title="切换主题">
                <span id="themeIcon">🌙</span>
            </button>

            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-item">
                    <span class="status-label">访客数量</span>
                    <span class="status-value" id="visitorCount">0</span>
                </div>
                <div class="status-item">
                    <span class="status-label">在线时长</span>
                    <span class="status-value" id="onlineTime">00:00:00</span>
                </div>
                <div class="status-item">
                    <span class="status-label">页面加载</span>
                    <span class="status-value" id="loadTime">0ms</span>
                </div>
            </div>

            <!-- 实时时钟 -->
            <div class="clock-widget">
                <div class="clock-time" id="currentTime">00:00:00</div>
                <div class="clock-date" id="currentDate">2024-01-01</div>
            </div>

            <!-- 天气信息 -->
            <div class="weather-widget">
                <div class="weather-icon" id="weatherIcon">🌤️</div>
                <div class="weather-temp" id="weatherTemp">--°C</div>
                <div class="weather-desc" id="weatherDesc">获取中...</div>
            </div>

            <!-- 快捷搜索框 -->
            <div class="search-widget" id="searchWidget">
                <input type="text" class="search-input" id="searchInput" placeholder="输入搜索内容...">
                <div class="search-engines">
                    <button class="search-engine" data-engine="baidu">百度</button>
                    <button class="search-engine" data-engine="google">Google</button>
                    <button class="search-engine" data-engine="bing">必应</button>
                    <button class="search-engine" data-engine="github">GitHub</button>
                </div>
            </div>

            <!-- 便签功能 -->
            <div class="notes-widget">
                <div class="notes-header">
                    <span>📝 便签</span>
                    <span id="addNote" style="cursor: pointer;">+</span>
                </div>
                <textarea class="notes-input" id="notesInput" placeholder="输入便签内容..." style="display: none;"></textarea>
                <div class="notes-list" id="notesList"></div>
            </div>

            <!-- 音乐播放器 -->
            <div class="music-widget">
                <div style="font-size: 14px; font-weight: bold; margin-bottom: 5px;">🎵 白噪音</div>
                <div style="font-size: 12px; color: #666;" id="musicStatus">点击播放</div>
                <div class="music-controls">
                    <button class="music-btn" id="playBtn">▶️</button>
                    <button class="music-btn" id="stopBtn">⏹️</button>
                </div>
            </div>

            <!-- 帮助面板 -->
            <div class="help-panel" id="helpPanel">
                <span class="help-close" id="helpClose">×</span>
                <div class="help-header">🎯 功能指南</div>

                <div class="help-section">
                    <h4>⌨️ 快捷键</h4>
                    <div class="help-item">
                        <span>切换主题</span>
                        <span class="help-key">Ctrl + D</span>
                    </div>
                    <div class="help-item">
                        <span>快捷搜索</span>
                        <span class="help-key">Ctrl + K</span>
                    </div>
                    <div class="help-item">
                        <span>回到顶部</span>
                        <span class="help-key">Ctrl + H</span>
                    </div>
                    <div class="help-item">
                        <span>全屏模式</span>
                        <span class="help-key">F11</span>
                    </div>
                    <div class="help-item">
                        <span>新建便签</span>
                        <span class="help-key">Ctrl + N</span>
                    </div>
                    <div class="help-item">
                        <span>保存便签</span>
                        <span class="help-key">Ctrl + S</span>
                    </div>
                    <div class="help-item">
                        <span>关闭弹窗</span>
                        <span class="help-key">Esc</span>
                    </div>
                    <div class="help-item">
                        <span>显示帮助</span>
                        <span class="help-key">F1</span>
                    </div>
                </div>

                <div class="help-section">
                    <h4>🖱️ 鼠标操作</h4>
                    <div class="help-item">
                        <span>双击页面回到顶部</span>
                        <span>💡</span>
                    </div>
                    <div class="help-item">
                        <span>右键显示快捷菜单</span>
                        <span>💡</span>
                    </div>
                </div>

                <div class="help-section">
                    <h4>🔧 功能组件</h4>
                    <div class="help-item">
                        <span>实时时钟</span>
                        <span>🕐</span>
                    </div>
                    <div class="help-item">
                        <span>访客统计</span>
                        <span>👥</span>
                    </div>
                    <div class="help-item">
                        <span>在线时长</span>
                        <span>⏱️</span>
                    </div>
                    <div class="help-item">
                        <span>天气信息</span>
                        <span>🌤️</span>
                    </div>
                    <div class="help-item">
                        <span>便签功能</span>
                        <span>📝</span>
                    </div>
                    <div class="help-item">
                        <span>白噪音播放</span>
                        <span>🎵</span>
                    </div>
                    <div class="help-item">
                        <span>滚动进度条</span>
                        <span>📊</span>
                    </div>
                </div>
            </div>
            
            <!-- 主要内容包裹器 -->
            <div id="mainContent">
                <!-- 关于/欢迎信息盒子 -->
                <div class="about box">
                    <p style="text-align: left;">
                        <i class="ti-announcement"></i> Hi，欢迎你的到来~~ 🎉
                    </p>
                    <!-- 居中图片容器，用于 'ti.jpeg' -->
                    <div class="center-image-container">
                        <img src="https://moc.wobshare.us.kg/522" 
                             alt="Welcome Illustration"
                             style="pointer-events: none; user-select: none;" 
                             ondragstart="return false;" 
                             onmousedown="return false;">
                    </div>
                </div>

                <!-- 动态页面内容区域，例如卡片列表，将由 nav.js 填充 -->
                <div id="dynamicPageContent">
                    <!-- Content loaded by nav.js will go here -->
                </div>

                <!-- 页面底部页脚 -->
                <footer class="footer">
                    <div class="copyright">
                        <div>
                            <!-- [修改] 将硬编码的年份替换为带ID的span，以便JS更新 -->
                            <p style="text-align: center;">Powered by <a href="https://wob-nit.pages.dev/" target="_blank" rel="noopener noreferrer">𝓌𝑜𝒷</a> © 2023-<span id="current-year"></span></p>
                        </div>
                    </div>
                </footer>
            </div>
        </section>

        <!-- 回到顶部按钮 -->
        <div id="fixedBar" title="回到顶部">
            <svg fill="currentColor" viewBox="0 0 24 24" width="24" height="24">
                <path d="M16.036 19.59a1 1 0 0 1-.997.995H9.032a.996.996 0 0 1-.997-.996v-7.005H5.03c-1.1 0-1.36-.633-.578-1.416L11.33 4.29a1.003 1003 0 0 1 1.412 0l6.878 6.88c.782.78.523 1.415-.58 1.415h-3.004v7.005z"></path>
            </svg>
        </div>
    </div>

    <!-- 引入 jQuery 库 -->
    <script src="static/jquery.js"></script>
    <!-- 引入自定义导航逻辑 JS 文件 -->
    <script src="static/nav.js"></script>
    <!-- 内联 JavaScript，处理菜单交互和回到顶部功能 -->
    <script>
        $(document).ready(function() {
            // 获取DOM元素
            const $leftBar = $('#leftBar');
            const $menuToggle = $('#menu-toggle');
            const $closeMenuBtn = $('#closeMenuBtn');
            const $body = $('body');

            // 打开菜单的函数
            function openMenu() {
                $leftBar.addClass('active'); // 添加 active 类使左侧栏显示
                $body.addClass('menu-open'); // 为body添加类，可能用于阻止滚动或模糊背景
                $menuToggle.find('i').removeClass('ti-menu-alt').addClass('ti-close'); // 切换菜单图标为关闭图标
            }

            // 关闭菜单的函数
            function closeMenu() {
                $leftBar.removeClass('active'); // 移除 active 类使左侧栏隐藏
                $body.removeClass('menu-open'); // 移除body类
                $menuToggle.find('i').removeClass('ti-close').addClass('ti-menu-alt'); // 切换菜单图标为菜单图标
            }

            // 菜单切换按钮点击事件
            $menuToggle.on('click', function(e) {
                e.stopPropagation(); // 阻止事件冒泡，防止点击菜单按钮时触发document的点击事件
                if ($leftBar.hasClass('active')) {
                    closeMenu(); // 如果菜单已打开，则关闭
                } else {
                    openMenu(); // 如果菜单已关闭，则打开
                }
            });

            // 关闭菜单按钮点击事件
            $closeMenuBtn.on('click', function() {
                closeMenu();
            });

            // 导航项点击事件 (在移动端点击后自动关闭菜单)
            $('#navItem').on('click', 'a', function() {
                if ($(window).width() <= 768) { // 如果是移动端屏幕
                    closeMenu(); // 点击导航项后关闭菜单
                }
                $('#navItem a').removeClass('active-link'); // 移除所有导航项的激活状态
                $(this).addClass('active-link'); // 为当前点击的导航项添加激活状态
            });

            // 点击菜单外部区域关闭菜单
            $(document).on('click', function(event) {
                // 如果左侧栏处于激活状态，且点击的不是左侧栏本身也不是菜单切换按钮
                if ($leftBar.hasClass('active') &&
                    !$leftBar.is(event.target) && $leftBar.has(event.target).length === 0 &&
                    !$menuToggle.is(event.target) && $menuToggle.has(event.target).length === 0) {
                    if (!$(event.target).closest('#menu-toggle').length) { // 再次确认点击的不是菜单切换按钮或其子元素
                         closeMenu(); // 关闭菜单
                    }
                }
            });

            // 回到顶部按钮的显示/隐藏逻辑
            const $fixedBar = $('#fixedBar');
            $fixedBar.hide(); // 页面加载时默认隐藏

            $(window).scroll(function() {
                if ($(this).scrollTop() > 200) { // 当滚动距离超过200px时
                    $fixedBar.fadeIn(); // 淡入显示回到顶部按钮
                } else {
                    $fixedBar.fadeOut(); // 否则淡出隐藏
                }
            });

            // 回到顶部按钮点击事件
            $fixedBar.on('click', function() {
                $('html, body').animate({ scrollTop: 0 }, 'smooth'); // 平滑滚动到页面顶部
                return false; // 阻止默认的链接行为
            });

            // 禁止图片拖拽
            $('img').on('dragstart', function(event) { event.preventDefault(); });

            // [新增] 自动更新页脚的年份
            $('#current-year').text(new Date().getFullYear());

            // 主题切换功能
            const $themeToggle = $('#themeToggle');
            const $themeIcon = $('#themeIcon');

            // 检查本地存储的主题设置
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                $body.addClass('dark-theme');
                $themeIcon.text('☀️');
            }

            $themeToggle.on('click', function() {
                $body.toggleClass('dark-theme');
                const isDark = $body.hasClass('dark-theme');
                $themeIcon.text(isDark ? '☀️' : '🌙');
                localStorage.setItem('theme', isDark ? 'dark' : 'light');
            });

            // 访客计数功能
            function updateVisitorCount() {
                let count = localStorage.getItem('visitorCount') || 0;
                count = parseInt(count) + 1;
                localStorage.setItem('visitorCount', count);
                $('#visitorCount').text(count.toLocaleString());
            }
            updateVisitorCount();

            // 在线时长计算
            let startTime = Date.now();
            function updateOnlineTime() {
                const elapsed = Date.now() - startTime;
                const hours = Math.floor(elapsed / 3600000);
                const minutes = Math.floor((elapsed % 3600000) / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);
                $('#onlineTime').text(
                    `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
                );
            }
            setInterval(updateOnlineTime, 1000);

            // 页面加载时间
            window.addEventListener('load', function() {
                const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                $('#loadTime').text(loadTime + 'ms');
            });

            // 实时时钟
            function updateClock() {
                const now = new Date();
                const time = now.toLocaleTimeString('zh-CN', { hour12: false });
                const date = now.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    weekday: 'short'
                });
                $('#currentTime').text(time);
                $('#currentDate').text(date);
            }
            updateClock();
            setInterval(updateClock, 1000);

            // 网站性能监控
            function getNetworkInfo() {
                if ('connection' in navigator) {
                    const connection = navigator.connection;
                    return {
                        effectiveType: connection.effectiveType,
                        downlink: connection.downlink,
                        rtt: connection.rtt
                    };
                }
                return null;
            }

            // 快捷搜索功能
            const $searchWidget = $('#searchWidget');
            const $searchInput = $('#searchInput');

            // 添加键盘快捷键
            $(document).keydown(function(e) {
                // Ctrl + D 切换主题
                if (e.ctrlKey && e.key === 'd') {
                    e.preventDefault();
                    $themeToggle.click();
                }
                // Ctrl + H 回到顶部
                if (e.ctrlKey && e.key === 'h') {
                    e.preventDefault();
                    $fixedBar.click();
                }
                // Ctrl + K 打开搜索
                if (e.ctrlKey && e.key === 'k') {
                    e.preventDefault();
                    $searchWidget.fadeIn();
                    $searchInput.focus();
                }
                // Esc 关闭菜单或搜索框
                if (e.key === 'Escape') {
                    closeMenu();
                    $searchWidget.fadeOut();
                }
                // Enter 执行搜索
                if (e.key === 'Enter' && $searchWidget.is(':visible')) {
                    const query = $searchInput.val().trim();
                    if (query) {
                        performSearch(query, 'baidu');
                    }
                }
            });

            // 搜索引擎配置
            const searchEngines = {
                baidu: 'https://www.baidu.com/s?wd=',
                google: 'https://www.google.com/search?q=',
                bing: 'https://www.bing.com/search?q=',
                github: 'https://github.com/search?q='
            };

            // 执行搜索
            function performSearch(query, engine) {
                const url = searchEngines[engine] + encodeURIComponent(query);
                window.open(url, '_blank');
                $searchWidget.fadeOut();
                $searchInput.val('');
            }

            // 搜索引擎按钮点击事件
            $('.search-engine').on('click', function() {
                const engine = $(this).data('engine');
                const query = $searchInput.val().trim();
                if (query) {
                    performSearch(query, engine);
                }
            });

            // 点击搜索框外部关闭
            $searchWidget.on('click', function(e) {
                if (e.target === this) {
                    $searchWidget.fadeOut();
                }
            });

            // 天气信息获取
            function getWeatherInfo() {
                // 使用免费的天气API获取天气信息
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(function(position) {
                        const lat = position.coords.latitude;
                        const lon = position.coords.longitude;

                        // 这里使用一个简单的天气模拟，实际项目中可以接入真实的天气API
                        const weatherConditions = [
                            { icon: '☀️', temp: '25', desc: '晴朗' },
                            { icon: '⛅', temp: '22', desc: '多云' },
                            { icon: '🌤️', temp: '20', desc: '晴转多云' },
                            { icon: '🌧️', temp: '18', desc: '小雨' }
                        ];

                        const randomWeather = weatherConditions[Math.floor(Math.random() * weatherConditions.length)];
                        $('#weatherIcon').text(randomWeather.icon);
                        $('#weatherTemp').text(randomWeather.temp + '°C');
                        $('#weatherDesc').text(randomWeather.desc);
                    }, function() {
                        $('#weatherDesc').text('位置获取失败');
                    });
                } else {
                    $('#weatherDesc').text('不支持定位');
                }
            }

            // 初始化天气信息
            setTimeout(getWeatherInfo, 2000);

            // 网站统计功能
            function updateSiteStats() {
                // 页面访问统计
                let pageViews = localStorage.getItem('pageViews') || 0;
                pageViews = parseInt(pageViews) + 1;
                localStorage.setItem('pageViews', pageViews);

                // 今日访问统计
                const today = new Date().toDateString();
                const todayKey = 'visits_' + today;
                let todayVisits = localStorage.getItem(todayKey) || 0;
                todayVisits = parseInt(todayVisits) + 1;
                localStorage.setItem(todayKey, todayVisits);
            }
            updateSiteStats();

            // 添加页面可见性API，当页面不可见时暂停计时器
            let isPageVisible = true;
            document.addEventListener('visibilitychange', function() {
                isPageVisible = !document.hidden;
                if (!isPageVisible) {
                    // 页面隐藏时的处理
                } else {
                    // 页面重新可见时重新开始计时
                    startTime = Date.now() - (parseInt($('#onlineTime').text().split(':')[0]) * 3600000 +
                               parseInt($('#onlineTime').text().split(':')[1]) * 60000 +
                               parseInt($('#onlineTime').text().split(':')[2]) * 1000);
                }
            });

            // 添加右键菜单功能
            $(document).on('contextmenu', function(e) {
                e.preventDefault();
                // 可以在这里添加自定义右键菜单
            });

            // 添加双击回到顶部功能
            $(document).on('dblclick', function(e) {
                if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                    $('html, body').animate({ scrollTop: 0 }, 'smooth');
                }
            });

            // 添加滚动进度条
            function updateScrollProgress() {
                const scrollTop = $(window).scrollTop();
                const docHeight = $(document).height() - $(window).height();
                const scrollPercent = (scrollTop / docHeight) * 100;

                // 创建进度条如果不存在
                if ($('#scrollProgress').length === 0) {
                    $('body').append('<div id="scrollProgress" style="position: fixed; top: 0; left: 0; width: 0%; height: 3px; background: linear-gradient(90deg, #007bff, #4fc3f7); z-index: 9999; transition: width 0.1s ease;"></div>');
                }

                $('#scrollProgress').css('width', scrollPercent + '%');
            }

            $(window).on('scroll', updateScrollProgress);

            // 添加页面加载动画
            function showLoadingAnimation() {
                const loadingHtml = `
                    <div id="pageLoading" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255,255,255,0.9); z-index: 9999; display: flex; align-items: center; justify-content: center; flex-direction: column;">
                        <div style="width: 50px; height: 50px; border: 3px solid #f3f3f3; border-top: 3px solid #007bff; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                        <p style="margin-top: 20px; color: #333;">加载中...</p>
                    </div>
                    <style>
                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                    </style>
                `;
                $('body').append(loadingHtml);

                // 页面加载完成后隐藏动画
                $(window).on('load', function() {
                    $('#pageLoading').fadeOut(500, function() {
                        $(this).remove();
                    });
                });
            }

            // 如果页面还在加载中，显示加载动画
            if (document.readyState === 'loading') {
                showLoadingAnimation();
            }

            // 便签功能
            let notes = JSON.parse(localStorage.getItem('userNotes') || '[]');

            function renderNotes() {
                const $notesList = $('#notesList');
                $notesList.empty();
                notes.forEach((note, index) => {
                    const $noteItem = $(`
                        <div class="note-item" data-index="${index}">
                            ${note.content}
                            <span class="note-delete" data-index="${index}">×</span>
                        </div>
                    `);
                    $notesList.append($noteItem);
                });
            }

            function saveNotes() {
                localStorage.setItem('userNotes', JSON.stringify(notes));
            }

            // 添加便签
            $('#addNote').on('click', function() {
                const $input = $('#notesInput');
                if ($input.is(':visible')) {
                    const content = $input.val().trim();
                    if (content) {
                        notes.unshift({
                            content: content,
                            timestamp: new Date().toLocaleString()
                        });
                        saveNotes();
                        renderNotes();
                        $input.val('').hide();
                    }
                } else {
                    $input.show().focus();
                }
            });

            // 删除便签
            $(document).on('click', '.note-delete', function() {
                const index = $(this).data('index');
                notes.splice(index, 1);
                saveNotes();
                renderNotes();
            });

            // 便签输入框回车事件
            $('#notesInput').on('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    $('#addNote').click();
                }
            });

            renderNotes();

            // 白噪音播放器
            let audioContext;
            let whiteNoiseNode;
            let isPlaying = false;

            function createWhiteNoise() {
                if (!audioContext) {
                    audioContext = new (window.AudioContext || window.webkitAudioContext)();
                }

                const bufferSize = 2 * audioContext.sampleRate;
                const noiseBuffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
                const output = noiseBuffer.getChannelData(0);

                for (let i = 0; i < bufferSize; i++) {
                    output[i] = Math.random() * 2 - 1;
                }

                whiteNoiseNode = audioContext.createBufferSource();
                whiteNoiseNode.buffer = noiseBuffer;
                whiteNoiseNode.loop = true;

                const gainNode = audioContext.createGain();
                gainNode.gain.value = 0.1; // 降低音量

                whiteNoiseNode.connect(gainNode);
                gainNode.connect(audioContext.destination);

                return whiteNoiseNode;
            }

            $('#playBtn').on('click', function() {
                if (!isPlaying) {
                    try {
                        const noise = createWhiteNoise();
                        noise.start();
                        isPlaying = true;
                        $('#musicStatus').text('播放中...');
                        $(this).text('⏸️');
                    } catch (error) {
                        $('#musicStatus').text('播放失败');
                    }
                } else {
                    if (whiteNoiseNode) {
                        whiteNoiseNode.stop();
                        isPlaying = false;
                        $('#musicStatus').text('已暂停');
                        $(this).text('▶️');
                    }
                }
            });

            $('#stopBtn').on('click', function() {
                if (whiteNoiseNode) {
                    whiteNoiseNode.stop();
                    isPlaying = false;
                    $('#musicStatus').text('已停止');
                    $('#playBtn').text('▶️');
                }
            });

            // 添加更多快捷键
            $(document).keydown(function(e) {
                // F11 全屏模式
                if (e.key === 'F11') {
                    e.preventDefault();
                    if (!document.fullscreenElement) {
                        document.documentElement.requestFullscreen();
                    } else {
                        document.exitFullscreen();
                    }
                }
                // Ctrl + S 保存便签
                if (e.ctrlKey && e.key === 's') {
                    e.preventDefault();
                    if ($('#notesInput').is(':visible')) {
                        $('#addNote').click();
                    }
                }
                // Ctrl + N 新建便签
                if (e.ctrlKey && e.key === 'n') {
                    e.preventDefault();
                    $('#addNote').click();
                }
            });

            // 添加网站性能监控
            function monitorPerformance() {
                if ('performance' in window) {
                    const navigation = performance.getEntriesByType('navigation')[0];
                    const loadTime = navigation.loadEventEnd - navigation.loadEventStart;
                    const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;

                    console.log('页面加载时间:', loadTime + 'ms');
                    console.log('DOM加载时间:', domContentLoaded + 'ms');
                }
            }

            // 页面加载完成后监控性能
            $(window).on('load', function() {
                setTimeout(monitorPerformance, 1000);
            });

            // 添加错误监控
            window.addEventListener('error', function(e) {
                console.error('页面错误:', e.error);
                // 可以在这里添加错误上报逻辑
            });

            // 添加网络状态监控
            function updateNetworkStatus() {
                const isOnline = navigator.onLine;
                if (!isOnline) {
                    // 显示离线提示
                    if ($('#offlineNotice').length === 0) {
                        $('body').append(`
                            <div id="offlineNotice" style="position: fixed; top: 0; left: 0; width: 100%; background: #ff4757; color: white; text-align: center; padding: 10px; z-index: 9999;">
                                网络连接已断开，部分功能可能无法使用
                            </div>
                        `);
                    }
                } else {
                    $('#offlineNotice').remove();
                }
            }

            window.addEventListener('online', updateNetworkStatus);
            window.addEventListener('offline', updateNetworkStatus);
            updateNetworkStatus();

            // 添加页面标题动态效果
            let originalTitle = document.title;
            let titleIndex = 0;
            const titleAnimation = ['🌟', '✨', '💫', '⭐'];

            function animateTitle() {
                if (document.hidden) {
                    document.title = '💤 ' + originalTitle + ' - 页面已隐藏';
                } else {
                    document.title = titleAnimation[titleIndex] + ' ' + originalTitle;
                    titleIndex = (titleIndex + 1) % titleAnimation.length;
                }
            }

            setInterval(animateTitle, 2000);

            // 页面可见性变化时更新标题
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    document.title = originalTitle;
                }
            });

            // 添加鼠标右键自定义菜单
            let customContextMenu = null;

            $(document).on('contextmenu', function(e) {
                e.preventDefault();

                if (customContextMenu) {
                    customContextMenu.remove();
                }

                customContextMenu = $(`
                    <div style="position: fixed; background: white; border: 1px solid #ccc; border-radius: 5px; padding: 5px; z-index: 9999; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div class="context-menu-item" data-action="refresh">🔄 刷新页面</div>
                        <div class="context-menu-item" data-action="theme">🌙 切换主题</div>
                        <div class="context-menu-item" data-action="search">🔍 快捷搜索</div>
                        <div class="context-menu-item" data-action="top">⬆️ 回到顶部</div>
                    </div>
                `).css({
                    left: e.pageX,
                    top: e.pageY
                });

                $('body').append(customContextMenu);

                // 添加菜单项样式
                $('.context-menu-item').css({
                    padding: '8px 12px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    borderBottom: '1px solid #eee'
                }).hover(function() {
                    $(this).css('background', '#f0f0f0');
                }, function() {
                    $(this).css('background', 'white');
                });

                // 菜单项点击事件
                $('.context-menu-item').on('click', function() {
                    const action = $(this).data('action');
                    switch(action) {
                        case 'refresh':
                            location.reload();
                            break;
                        case 'theme':
                            $themeToggle.click();
                            break;
                        case 'search':
                            $searchWidget.fadeIn();
                            $searchInput.focus();
                            break;
                        case 'top':
                            $fixedBar.click();
                            break;
                    }
                    customContextMenu.remove();
                });
            });

            // 点击其他地方关闭自定义菜单
            $(document).on('click', function() {
                if (customContextMenu) {
                    customContextMenu.remove();
                }
            });

            // 帮助面板功能
            const $helpPanel = $('#helpPanel');
            const $helpClose = $('#helpClose');

            // F1 显示帮助
            $(document).keydown(function(e) {
                if (e.key === 'F1') {
                    e.preventDefault();
                    $helpPanel.fadeIn();
                }
            });

            // 关闭帮助面板
            $helpClose.on('click', function() {
                $helpPanel.fadeOut();
            });

            // 点击面板外部关闭
            $helpPanel.on('click', function(e) {
                if (e.target === this) {
                    $helpPanel.fadeOut();
                }
            });

            // 添加欢迎提示
            function showWelcomeMessage() {
                if (!localStorage.getItem('welcomeShown')) {
                    setTimeout(function() {
                        const welcomeMsg = $(`
                            <div id="welcomeMessage" style="position: fixed; top: 20px; left: 50%; transform: translateX(-50%); background: linear-gradient(45deg, #007bff, #4fc3f7); color: white; padding: 15px 25px; border-radius: 25px; z-index: 9999; box-shadow: 0 4px 15px rgba(0,0,0,0.2); animation: slideDown 0.5s ease;">
                                🎉 欢迎使用 𝓌𝑜𝒷 网址导航！按 F1 查看功能指南
                                <span style="margin-left: 15px; cursor: pointer; font-weight: bold;" onclick="$(this).parent().fadeOut();">×</span>
                            </div>
                            <style>
                                @keyframes slideDown {
                                    from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                                    to { transform: translateX(-50%) translateY(0); opacity: 1; }
                                }
                            </style>
                        `);
                        $('body').append(welcomeMsg);

                        setTimeout(function() {
                            welcomeMsg.fadeOut(function() {
                                $(this).remove();
                            });
                        }, 5000);

                        localStorage.setItem('welcomeShown', 'true');
                    }, 2000);
                }
            }

            showWelcomeMessage();

            // 添加页面统计信息到控制台
            console.log(`
            🎯 𝓌𝑜𝒷 网址导航 - 功能统计
            ================================
            ✅ 主题切换功能
            ✅ 实时时钟显示
            ✅ 访客数量统计
            ✅ 在线时长计算
            ✅ 页面加载时间
            ✅ 天气信息显示
            ✅ 快捷搜索功能
            ✅ 便签记录功能
            ✅ 白噪音播放
            ✅ 滚动进度条
            ✅ 键盘快捷键
            ✅ 自定义右键菜单
            ✅ 网络状态监控
            ✅ 页面性能监控
            ✅ 全屏模式支持
            ✅ 响应式设计
            ================================
            按 F1 查看详细功能指南
            `);

            // 添加开发者彩蛋
            let konamiCode = [];
            const konamiSequence = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'KeyB', 'KeyA'];

            $(document).keydown(function(e) {
                konamiCode.push(e.code);
                if (konamiCode.length > konamiSequence.length) {
                    konamiCode.shift();
                }

                if (JSON.stringify(konamiCode) === JSON.stringify(konamiSequence)) {
                    // 彩蛋效果
                    $('body').append(`
                        <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 48px; z-index: 9999; animation: rainbow 2s infinite;">
                            🎉 恭喜发现彩蛋！🎉
                        </div>
                        <style>
                            @keyframes rainbow {
                                0% { color: red; }
                                16% { color: orange; }
                                33% { color: yellow; }
                                50% { color: green; }
                                66% { color: blue; }
                                83% { color: indigo; }
                                100% { color: violet; }
                            }
                        </style>
                    `);

                    setTimeout(function() {
                        $('body').children().last().fadeOut(function() {
                            $(this).remove();
                        });
                    }, 3000);

                    konamiCode = [];
                }
            });

        });
    </script>
</body>

</html>
