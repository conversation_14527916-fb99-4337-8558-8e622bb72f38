<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="description" content="𝓌𝑜𝒷网址导航" />
    <link rel="shortcut icon" href="static/logo.svg" type="image/svg+xml">
    <meta name="keywords" content="网址导航" />
    <title>𝓌𝑜𝒷网址导航</title>
    <!-- 引入外部样式表，用于基本布局和通用样式 -->
    <link rel="stylesheet" href="static/style.css">
    <!-- 引入 Themify Icons 字体图标库 -->
    <link rel="stylesheet" href="static/themify-icons.css">

    <!-- 内联样式表，用于覆盖或添加特定样式，特别是响应式和布局调整 -->
    <style>
        /* 针对大Logo图片和其容器的样式调整 */
        .big-logo {
            /* 保持之前修改，使其填充满容器且不留边距 */
            /* text-align: center; */ /* 移除，因为图片已设width:100%且是block，不需要text-align居中 */
            width: 100%; /* 宽度占满父容器，使其图片撑满250px的left-bar */
            /* max-width: 200px; */ /* 已移除，确保图片可以填充满left-bar的全部宽度 */
            margin: 0 0 5px 0; /* 移除水平自动外边距，使内容紧贴左右边缘。仅保留底部外边距5px。 */
            aspect-ratio: 302 / 306; /* 保持图片的宽高比 */
            overflow: hidden; /* 防止内容溢出 */
        }

        .big-logo img {
            width: 100%; /* 图片宽度填充父容器 */
            height: 100%; /* 图片高度填充父容器 */
            object-fit: cover; /* 裁剪图片以填充容器，保持宽高比 */
            display: block; /* 移除图片底部默认的空白间隙 */
        }

        /* ------------------------------------------- */
        /* 基础布局和主题颜色样式 */
        body {
            margin: 0; /* 移除body默认外边距，确保内容从屏幕边缘开始 */
            background-color: #f0f2f5; /* 页面背景色 */
            color: #333333; /* 默认文字颜色 */
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; /* 字体栈 */
            transition: padding-left 0.3s ease-in-out; /* 为菜单打开时的padding-left变化添加过渡效果（虽然这里没有实际用到padding-left） */
        }

        body.menu-open {
            overflow: hidden; /* 菜单打开时，禁止body滚动，防止背景内容滚动 */
        }

        .container {
            display: flex; /* 使用 Flexbox 布局，使左侧栏和主内容区并排显示 */
            min-height: 100vh; /* 容器最小高度为视口高度，确保页面即使内容较少也能撑满屏幕 */
        }

        /* 左侧导航栏样式 */
        .left-bar {
            width: 250px; /* 固定左侧栏宽度 */
            background-color: #fff; /* 背景色为白色 */
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1); /* 右侧阴影，增加立体感 */
            /* MODIFIED: PC端样式 - 移除左侧栏的外部填充，使其内部内容直接贴边 */
            padding: 0; 
            box-sizing: border-box; /* 边框盒模型，padding和border不增加元素总尺寸 */
            display: flex; /* 内部使用 Flexbox 布局 */
            flex-direction: column; /* 垂直排列子元素 */
            transition: transform 0.3s ease-in-out, left 0.3s ease-in-out; /* 菜单滑入滑出的过渡效果 */
            position: relative; /* 相对定位，为子元素（如关闭按钮）的绝对定位提供参考 */
            z-index: 1000; /* 层级高于主内容区 */
        }
        
        /* 移动端菜单关闭按钮样式 */
        .close-menu-btn {
            display: none; /* 默认不显示，只在移动端显示 */
            position: absolute; /* 绝对定位 */
            top: 10px; /* 距离顶部10px */
            right: 15px; /* 距离右侧15px */
            font-size: 2em; /* 字体大小 */
            color: #333; /* 颜色 */
            text-decoration: none; /* 移除下划线 */
            line-height: 1; /* 行高 */
            z-index: 1101; /* 层级高于左侧栏内容 */
        }

        /* 网站标题样式 */
        .title {
            text-align: center; /* 文本居中 */
            /* MODIFIED: PC端样式 - 移除底部外边距，让标题更紧贴容器顶部或Logo */
            margin: 0; /* 从 margin-bottom: 20px; 改变 */
            font-size: 1.4em; /* 字体大小 */
            color: #333333; /* 文本颜色 */
            padding: 0; /* 默认内边距为0 */
            box-sizing: border-box;
        }

        /* 导航列表样式 */
        .nav-item {
            list-style: none; /* 移除列表项前面的点 */
            padding: 0; /* 移除列表默认内边距 */
            margin: 0; /* 移除列表默认外边距 */
            flex-grow: 1; /* 占据可用空间，使底部评论区固定在底部 */
            overflow-y: auto; /* 当内容溢出时，允许垂直滚动 */
        }

        /* 导航链接样式 */
        .nav-item li a {
            display: block; /* 使链接填充整个列表项区域，便于点击 */
            padding: 12px 15px; /* 默认内边距，确保导航文字可读性，这属于内容内部间距 */
            text-decoration: none; /* 移除下划线 */
            color: #333333; /* 默认文字颜色 */
            border-radius: 0; /* 移除圆角，避免视觉上的“缝隙” */
            transition: background-color 0.3s ease; /* 背景色变化的过渡效果 */
        }

        /* 导航链接悬停和激活时的样式 */
        .nav-item li a:hover,
        .nav-item li a.active-link {
            background-color: #e9e9e9; /* 悬停/激活时的背景色 */
            color: #007bff; /* 悬停/激活时的文字颜色 */
        }

        /* 底部评论区/ICP信息样式 */
        .item.comment {
            /* MODIFIED: PC端样式 - 移除顶部外边距，使其紧贴上方导航区 */
            margin: 0; /* 从 margin-top: 20px; 改变 */
            text-align: center; /* 文本居中 */
            flex-shrink: 0; /* 不会被压缩 */
            padding: 0; /* 默认内边距为0 */
            box-sizing: border-box;
        }

        .icp a {
            color: #999999; /* 链接颜色 */
            text-decoration: none; /* 移除下划线 */
            font-size: 0.9em; /* 字体大小 */
        }

        /* 主内容区样式 */
        .main {
            flex-grow: 1; /* 占据剩余所有可用空间 */
            /* MODIFIED: PC端样式 - 移除主内容区的外部填充，使其内容充满可用空间 */
            padding: 0; /* 从 20px 改变 */
            position: relative; /* 相对定位，为内部元素（如菜单切换按钮）的绝对定位提供参考 */
            transition: filter 0.3s ease-in-out; /* 滤镜效果的过渡（如果使用了） */
        }
        body.menu-open .main {
            /* filter: blur(2px) brightness(0.7); */
        }

        /* 移动端菜单切换按钮样式 */
        #menu-toggle {
            display: none; /* 默认不显示，只在移动端显示 */
            font-size: 1.8em; /* 字体大小 */
            padding: 10px; /* 内边距 */
            position: fixed; /* 固定定位，不随滚动条滚动 */
            top: 10px; /* 距离顶部10px */
            left: 10px; /* 距离左侧10px */
            z-index: 1001; /* 层级高于普通内容 */
            background-color: rgba(255,255,255,0.8); /* 半透明背景 */
            border-radius: 5px; /* 圆角 */
            box-shadow: 0 2px 5px rgba(0,0,0,0.1); /* 阴影 */
            color: #333; /* 颜色 */
            cursor: pointer; /* 鼠标悬停时显示手型指针 */
        }
        #menu-toggle i {
            display: block; /* 使图标块级显示 */
        }

        /* "关于"区域盒子样式 */
        .about.box {
            background-color: #fff; /* 背景色 */
            padding: 20px; /* 内边距，保持内容块内部填充，有助于可读性 */
            border-radius: 8px; /* 圆角 */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* 阴影 */
            margin-bottom: 20px; /* 底部外边距，与下方内容块隔开 */
        }

        .about.box p {
            color: #333333; /* 文字颜色 */
            margin-top: 0; /* 移除顶部外边距 */
            margin-bottom: 15px; /* 底部外边距 */
            font-size: 1.1em; /* 字体大小 */
            line-height: 1.6; /* 行高 */
        }
        .about.box .center-image-container {
            /* 保持text-align: center; 以确保对inline/inline-block子元素居中，
               辅助block元素图片的 margin: auto; */
            text-align: center; 
        }
        .about.box .center-image-container img {
            max-width: 100%; /* 图片最大宽度不超过父容器 */
            width: 100%; /* 图片宽度填充父容器的100% */
            /* MODIFIED: 调整图片显示大小 - 减小 max-width 值为 300px，使其显示小一点 */
            max-width: 300px; /* 原为 400px */
            height: auto; /* 高度自动调整，保持图片原始比例 */
            object-fit: cover; /* 裁剪图片以填充容器，保持宽高比。如果容器宽高比与图片不同，图片会被裁剪。 */
            display: block; /* 块级显示，以便应用 margin: auto 进行水平居中 */
            margin: 0 auto; /* 水平居中图片，上下外边距为0 */
            pointer-events: none; /* 禁止鼠标事件，如点击拖拽 */
            user-select: none; /* 禁止用户选择文本或图片 */
        }

        /* 页面底部页脚样式 */
        .footer {
            text-align: center; /* 文本居中 */
            padding: 20px 0; /* 上下内边距20px，左右0，保持内容块内部填充 */
            color: #777777; /* 文字颜色 */
            font-size: 0.9em; /* 字体大小 */
        }

        .footer a {
            color: #999999; /* 链接颜色 */
            text-decoration: none; /* 移除下划线 */
        }
         .footer a:hover {
            text-decoration: underline; /* 鼠标悬停时显示下划线 */
        }

        /* 回到顶部按钮样式 */
        #fixedBar {
            position: fixed; /* 固定定位，不随滚动条滚动 */
            bottom: 20px; /* 距离底部20px */
            right: 20px; /* 距离右侧20px */
            cursor: pointer; /* 鼠标悬停时显示手型指针 */
            color: #555; /* 图标颜色 */
            background-color: rgba(255,255,255,0.8); /* 半透明背景 */
            border-radius: 50%; /* 圆形 */
            padding: 8px; /* 内边距 */
            box-shadow: 0 2px 5px rgba(0,0,0,0.15); /* 阴影 */
            transition: background-color 0.3s, color 0.3s; /* 背景色和颜色变化的过渡效果 */
        }
        #fixedBar:hover {
            background-color: #007bff; /* 悬停时背景色 */
            color: #fff; /* 悬停时图标颜色 */
        }

        #fixedBar svg {
            width: 24px; /* SVG图标宽度 */
            height: 24px; /* SVG图标高度 */
            display: block; /* 块级显示 */
        }

        /* 新增：动态加载内容区域的样式，特别是卡片布局 */
        #dynamicPageContent {
            display: grid; /* 使用 CSS Grid 布局 */
            /* 自动填充列，每列最小250px，最大1fr (占据剩余空间) */
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); 
            gap: 20px; /* 卡片之间的间距 */
            /* 因为 .main 的 padding 移除了，这里给卡片区域加上内边距，确保内容不贴边 */
            padding: 20px; 
        }

        /* 单个卡片项的样式 */
        .card-item {
            background-color: #fff; /* 背景色 */
            border-radius: 8px; /* 圆角 */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* 阴影 */
            padding: 15px; /* 内边距 */
            display: flex; /* 使用 flexbox 布局卡片内部元素 */
            flex-direction: column; /* 垂直排列图标和文本 */
            align-items: flex-start; /* 内容左对齐 */
        }

        /* 卡片图标容器样式 */
        .card-item .card-icon {
            width: 50px; /* 图标容器的宽度 */
            /* MODIFIED for t2.jpeg: 拉长图标容器的高度，使其能容纳被拉伸的图片 */
            height: 70px; /* 增加高度以允许图片“拉长”或为文本提供更多空间 */
            margin-bottom: 10px; /* 图标和标题之间的间距 */
            display: flex; /* 使用 flexbox 居中图标 */
            justify-content: center; /* 水平居中 */
            align-items: center; /* 垂直居中 */
        }

        /* 卡片图标图片样式 */
        .card-item .card-icon img {
            max-width: 100%; /* 图片最大宽度不超过容器 */
            max-height: 100%; /* 图片最大高度不超过容器 */
            /* MODIFIED for t2.jpeg: 图片拉长，填充满其容器 */
            object-fit: fill; /* 这将拉伸图片以填充其容器的尺寸，可能会导致图片变形。 */
            /* 如果您希望图片保持比例而不变形，但仍然占据容器高度，请使用 object-fit: contain; */
            /* object-fit: contain; */ 
        }

        /* 卡片标题样式 */
        .card-item h3 {
            font-size: 1.1em; /* 字体大小 */
            margin-top: 0; /* 移除顶部外边距 */
            margin-bottom: 5px; /* 底部外边距 */
            color: #333; /* 颜色 */
        }

        /* 卡片描述文字样式 */
        .card-item p.card-description {
            font-size: 0.9em; /* 字体大小 */
            color: #666; /* 颜色 */
            line-height: 1.4; /* 行高，确保文字行高足够，防止文字堆叠 */
            margin: 0; /* 移除默认外边距 */
            /* 确保文字不会被裁剪，并正常显示多行 */
            white-space: normal; /* 允许文本自动换行 */
            overflow: visible; /* 阻止内容被隐藏 */
            text-overflow: clip; /* 文本溢出时，不显示省略号（剪裁） */
            /* 如果您需要限制描述为2行并显示省略号，可以取消以下注释： */
            /* display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden; */
        }


        /* 媒体查询，用于响应式设计 (移动端样式) */
        @media (max-width: 768px) {
            /* 移动端左侧栏样式 */
            .left-bar {
                position: fixed; /* 固定定位，覆盖整个屏幕 */
                top: 0; /* 贴紧顶部 */
                left: -100%; /* 默认隐藏在屏幕左侧之外 */
                width: 100%; /* 宽度占满屏幕 */
                height: 100vh; /* 高度占满视口 */
                z-index: 1100; /* 高层级，确保覆盖内容 */
                box-shadow: 2px 0 10px rgba(0,0,0,0.2); /* 阴影 */
                /* 移动端左侧栏的填充保持不变，因为它需要为关闭按钮保留顶部空间，以及其他内部元素的填充 */
                padding: 50px 0 0 0; /* 顶部内边距50px，左右下为0 */
                margin: 0; /* 移除外边距 */
                box-sizing: border-box;
            }

            .left-bar.active {
                left: 0; /* 当菜单激活时，滑入屏幕 */
            }
            
            /* 移动端菜单关闭按钮显示 */
            .close-menu-btn {
                display: block; /* 在移动端显示关闭按钮 */
                right: 10px; /* 距离右侧10px */
            }

            /* 移动端菜单切换按钮显示 */
            #menu-toggle {
                display: block; /* 在移动端显示菜单切换按钮 */
            }
            
            /* MODIFIED: 移除移动端左侧栏内部所有内容的填充/外边距，实现内容真正贴边 */
            .big-logo {
                max-width: 100%; /* 在移动端 Logo 宽度填充100% */
                margin: 0; /* 无外边距 */
                padding: 0; /* 无内边距 */
                box-sizing: border-box;
            }
             .title {
                font-size: 1.2em; /* 移动端标题字体略小 */
                margin: 0; /* 无外边距 */
                padding: 0; /* 无内边距 */
            }
            .nav-item {
                padding: 0; /* 无内边距 */
                margin: 0; /* 无外边距 */
            }
            .nav-item li a {
                padding: 0; /* 移动端导航链接无内边距，文字会贴着边缘 */
                border-radius: 0; /* 确保无圆角，避免视觉缝隙 */
            }
            .item.comment {
                margin: 0; /* 无外边距 */
                padding: 0; /* 无内边距 */
            }
            .item.comment .icp {
                margin: 0; /* 无外边距 */
                padding: 0; /* 无内边距 */
            }
            .item.comment .icp a {
                padding: 0; /* 无内边距 */
            }
            /* 移动端卡片区域的内边距，相比PC端略小，以适应小屏幕 */
            #dynamicPageContent {
                padding: 10px; 
            }
        }
    </style>
</head>

<body>
    <!-- 主容器，包含左侧导航栏和主内容区 -->
    <div class="container" id="container">
        <!-- 左侧导航栏 -->
        <aside class="left-bar" id="leftBar">
            <!-- 移动端关闭菜单按钮 -->
            <a href="javascript:void(0);" class="close-menu-btn" id="closeMenuBtn">×</a>
            <!-- 网站大Logo区域 -->
            <div class="big-logo">
                <img src="https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/image/GIF/35465.gif" 
                     ondragstart="return false;" 
                     onmousedown="return false;"
                     alt="WOB Logo">
            </div>
            <!-- 网站标题 -->
            <div class="title" style="font-family: 'STKaiti', '华文楷体', serif;">
                <p>𝓌𝑜𝒷 网址导航</p>
            </div>
            <!-- 导航菜单 -->
            <nav class="nav">
                <ul class="nav-item" id="navItem">
                    <!-- 导航项将由 JS 动态填充 -->
                </ul>
            </nav>
            <!-- 底部评论/ICP信息 -->
            <div class="item comment">
                <p class="icp"><a href="https://wob-nit.pages.dev/">𝓌𝑜𝒷导航网站</a></p>
            </div>
        </aside>

        <!-- 主内容区域 -->
        <section class="main" id="mainSection">
            <!-- 移动端菜单切换按钮 -->
            <div id="menu-toggle">
                <i class="ti-menu-alt"></i>
            </div>
            
            <!-- 主要内容包裹器 -->
            <div id="mainContent">
                <!-- 关于/欢迎信息盒子 -->
                <div class="about box">
                    <p style="text-align: left;">
                        <i class="ti-announcement"></i> Hi，欢迎你的到来~~ 🎉
                    </p>
                    <!-- 居中图片容器，用于 'ti.jpeg' -->
                    <div class="center-image-container">
                        <img src="https://moc.wobshare.us.kg/522" 
                             alt="Welcome Illustration"
                             style="pointer-events: none; user-select: none;" 
                             ondragstart="return false;" 
                             onmousedown="return false;">
                    </div>
                </div>

                <!-- 动态页面内容区域，例如卡片列表，将由 nav.js 填充 -->
                <div id="dynamicPageContent">
                    <!-- Content loaded by nav.js will go here -->
                </div>

                <!-- 页面底部页脚 -->
                <footer class="footer">
                    <div class="copyright">
                        <div>
                            <!-- [修改] 将硬编码的年份替换为带ID的span，以便JS更新 -->
                            <p style="text-align: center;">Powered by <a href="https://wob-nit.pages.dev/" target="_blank" rel="noopener noreferrer">𝓌𝑜𝒷</a> © 2023-<span id="current-year"></span></p>
                        </div>
                    </div>
                </footer>
            </div>
        </section>

        <!-- 回到顶部按钮 -->
        <div id="fixedBar" title="回到顶部">
            <svg fill="currentColor" viewBox="0 0 24 24" width="24" height="24">
                <path d="M16.036 19.59a1 1 0 0 1-.997.995H9.032a.996.996 0 0 1-.997-.996v-7.005H5.03c-1.1 0-1.36-.633-.578-1.416L11.33 4.29a1.003 1003 0 0 1 1.412 0l6.878 6.88c.782.78.523 1.415-.58 1.415h-3.004v7.005z"></path>
            </svg>
        </div>
    </div>

    <!-- 引入 jQuery 库 -->
    <script src="static/jquery.js"></script>
    <!-- 引入自定义导航逻辑 JS 文件 -->
    <script src="static/nav.js"></script>
    <!-- 内联 JavaScript，处理菜单交互和回到顶部功能 -->
    <script>
        $(document).ready(function() {
            // 获取DOM元素
            const $leftBar = $('#leftBar');
            const $menuToggle = $('#menu-toggle');
            const $closeMenuBtn = $('#closeMenuBtn');
            const $body = $('body');

            // 打开菜单的函数
            function openMenu() {
                $leftBar.addClass('active'); // 添加 active 类使左侧栏显示
                $body.addClass('menu-open'); // 为body添加类，可能用于阻止滚动或模糊背景
                $menuToggle.find('i').removeClass('ti-menu-alt').addClass('ti-close'); // 切换菜单图标为关闭图标
            }

            // 关闭菜单的函数
            function closeMenu() {
                $leftBar.removeClass('active'); // 移除 active 类使左侧栏隐藏
                $body.removeClass('menu-open'); // 移除body类
                $menuToggle.find('i').removeClass('ti-close').addClass('ti-menu-alt'); // 切换菜单图标为菜单图标
            }

            // 菜单切换按钮点击事件
            $menuToggle.on('click', function(e) {
                e.stopPropagation(); // 阻止事件冒泡，防止点击菜单按钮时触发document的点击事件
                if ($leftBar.hasClass('active')) {
                    closeMenu(); // 如果菜单已打开，则关闭
                } else {
                    openMenu(); // 如果菜单已关闭，则打开
                }
            });

            // 关闭菜单按钮点击事件
            $closeMenuBtn.on('click', function() {
                closeMenu();
            });

            // 导航项点击事件 (在移动端点击后自动关闭菜单)
            $('#navItem').on('click', 'a', function() {
                if ($(window).width() <= 768) { // 如果是移动端屏幕
                    closeMenu(); // 点击导航项后关闭菜单
                }
                $('#navItem a').removeClass('active-link'); // 移除所有导航项的激活状态
                $(this).addClass('active-link'); // 为当前点击的导航项添加激活状态
            });

            // 点击菜单外部区域关闭菜单
            $(document).on('click', function(event) {
                // 如果左侧栏处于激活状态，且点击的不是左侧栏本身也不是菜单切换按钮
                if ($leftBar.hasClass('active') &&
                    !$leftBar.is(event.target) && $leftBar.has(event.target).length === 0 &&
                    !$menuToggle.is(event.target) && $menuToggle.has(event.target).length === 0) {
                    if (!$(event.target).closest('#menu-toggle').length) { // 再次确认点击的不是菜单切换按钮或其子元素
                         closeMenu(); // 关闭菜单
                    }
                }
            });

            // 回到顶部按钮的显示/隐藏逻辑
            const $fixedBar = $('#fixedBar');
            $fixedBar.hide(); // 页面加载时默认隐藏

            $(window).scroll(function() {
                if ($(this).scrollTop() > 200) { // 当滚动距离超过200px时
                    $fixedBar.fadeIn(); // 淡入显示回到顶部按钮
                } else {
                    $fixedBar.fadeOut(); // 否则淡出隐藏
                }
            });

            // 回到顶部按钮点击事件
            $fixedBar.on('click', function() {
                $('html, body').animate({ scrollTop: 0 }, 'smooth'); // 平滑滚动到页面顶部
                return false; // 阻止默认的链接行为
            });

            // 禁止图片拖拽
            $('img').on('dragstart', function(event) { event.preventDefault(); });

            // [新增] 自动更新页脚的年份
            $('#current-year').text(new Date().getFullYear());

        });
    </script>
</body>

</html>
